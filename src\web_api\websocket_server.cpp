#include "web_api/websocket_server.h"
#include <iostream>
#include <algorithm>

namespace body_controller {
namespace web_api {

WebSocketServer::WebSocketServer(int port) 
    : port_(port), running_(false) {
    std::cout << "[WebSocketServer] Created WebSocket server on port " << port << std::endl;
}

WebSocketServer::~WebSocketServer() {
    Stop();
}

bool WebSocketServer::Initialize() {
    try {
        // 注意：这是一个简化的WebSocket实现
        // 在生产环境中，建议使用专门的WebSocket库如websocketpp或uWebSockets

        // 设置WebSocket处理器
        server_.set_mount_point("/", "./web");

        // WebSocket连接处理（简化实现）
        server_.Get("/ws", [this](const httplib::Request& req, httplib::Response& res) {
            // 检查是否是WebSocket升级请求
            if (req.get_header_value("Connection").find("Upgrade") != std::string::npos &&
                req.get_header_value("Upgrade") == "websocket") {

                // 处理WebSocket握手（简化版本）
                HandleWebSocketHandshake(req, res);
            } else {
                res.status = 400;
                res.set_content("Bad Request: Not a WebSocket request", "text/plain");
            }
        });

        // 设置错误处理器
        server_.set_error_handler([](const httplib::Request&, httplib::Response& res) {
            res.set_content("WebSocket Server Error", "text/plain");
            res.status = 500;
        });

        std::cout << "[WebSocketServer] WebSocket server initialized successfully" << std::endl;
        std::cout << "[WebSocketServer] Note: This is a simplified WebSocket implementation" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[WebSocketServer] Failed to initialize: " << e.what() << std::endl;
        return false;
    }
}

bool WebSocketServer::Start() {
    if (running_) {
        std::cout << "[WebSocketServer] Server is already running" << std::endl;
        return true;
    }
    
    // 在单独的线程中启动服务器
    server_thread_ = std::thread([this]() {
        std::cout << "[WebSocketServer] Starting WebSocket server on port " << port_ << std::endl;
        running_ = true;
        
        if (!server_.listen("0.0.0.0", port_)) {
            std::cerr << "[WebSocketServer] Failed to start server on port " << port_ << std::endl;
            running_ = false;
        }
    });
    
    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    if (running_) {
        std::cout << "[WebSocketServer] WebSocket server started successfully on port " << port_ << std::endl;
        return true;
    } else {
        if (server_thread_.joinable()) {
            server_thread_.join();
        }
        return false;
    }
}

void WebSocketServer::Stop() {
    if (!running_) {
        return;
    }
    
    std::cout << "[WebSocketServer] Stopping WebSocket server..." << std::endl;
    running_ = false;
    
    // 关闭所有WebSocket连接
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        // 注意：在简化的WebSocket实现中，socket是void*类型
        // 实际的socket关闭由底层HTTP库处理
        for (auto& client : clients_) {
            if (client.second.socket) {
                // 标记客户端为断开状态
                client.second.connected = false;
            }
        }
        clients_.clear();
    }
    
    server_.stop();
    
    if (server_thread_.joinable()) {
        server_thread_.join();
    }
    
    std::cout << "[WebSocketServer] WebSocket server stopped" << std::endl;
}

bool WebSocketServer::IsRunning() const {
    return running_;
}

int WebSocketServer::GetPort() const {
    return port_;
}

size_t WebSocketServer::GetClientCount() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    return clients_.size();
}

void WebSocketServer::BroadcastMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    auto it = clients_.begin();
    while (it != clients_.end()) {
        try {
            if (it->second.socket && it->second.connected) {
                // 注意：在简化的WebSocket实现中，socket是void*类型
                // 实际的消息发送由HTTP库的WebSocket处理器负责
                // 这里我们只记录要发送的消息，实际发送在WebSocket处理器中完成
                std::cout << "[WebSocketServer] Queuing message for client: " << it->first << std::endl;
                ++it;
            } else {
                // 移除断开的连接
                std::cout << "[WebSocketServer] Removing disconnected client: " << it->first << std::endl;
                it = clients_.erase(it);
            }
        } catch (const std::exception& e) {
            std::cerr << "[WebSocketServer] Error sending message to client " << it->first 
                      << ": " << e.what() << std::endl;
            it = clients_.erase(it);
        }
    }
    
    if (!clients_.empty()) {
        std::cout << "[WebSocketServer] Broadcasted message to " << clients_.size() << " clients" << std::endl;
    }
}

void WebSocketServer::SendMessageToClient(const std::string& client_id, const std::string& message) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it != clients_.end() && it->second.socket && it->second.connected) {
        try {
            // 注意：在简化的WebSocket实现中，socket是void*类型
            // 实际的消息发送由HTTP库的WebSocket处理器负责
            // 这里我们只记录要发送的消息，实际发送在WebSocket处理器中完成
            std::cout << "[WebSocketServer] Queuing message for client: " << client_id << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "[WebSocketServer] Error handling message for client " << client_id
                      << ": " << e.what() << std::endl;
            clients_.erase(it);
        }
    } else {
        std::cerr << "[WebSocketServer] Client not found or disconnected: " << client_id << std::endl;
    }
}

std::vector<std::string> WebSocketServer::GetConnectedClients() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    std::vector<std::string> client_ids;
    for (const auto& client : clients_) {
        if (client.second.connected) {
            client_ids.push_back(client.first);
        }
    }
    
    return client_ids;
}

void WebSocketServer::HandleWebSocketHandshake(const httplib::Request& req, httplib::Response& res) {
    // 简化的WebSocket握手处理
    // 在实际实现中，这里需要完整的WebSocket协议处理
    
    std::string client_id = GenerateClientId();
    
    // 设置WebSocket响应头
    res.set_header("Upgrade", "websocket");
    res.set_header("Connection", "Upgrade");
    res.set_header("Sec-WebSocket-Accept", GenerateWebSocketAccept(req.get_header_value("Sec-WebSocket-Key")));
    res.status = 101; // Switching Protocols
    
    // 创建WebSocket连接（这里是简化实现）
    // 实际实现需要使用专门的WebSocket库
    
    std::cout << "[WebSocketServer] WebSocket handshake completed for client: " << client_id << std::endl;
}

std::string WebSocketServer::GenerateClientId() {
    static std::atomic<int> counter{0};
    return "client_" + std::to_string(++counter);
}

std::string WebSocketServer::GenerateWebSocketAccept(const std::string& key) {
    // 简化的WebSocket Accept生成
    // 实际实现需要按照RFC 6455标准
    return key + "_accept";
}

void WebSocketServer::OnClientConnected(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    WebSocketClient client;
    client.id = client_id;
    client.connected = true;
    client.connect_time = std::chrono::steady_clock::now();
    
    clients_[client_id] = client;
    
    std::cout << "[WebSocketServer] Client connected: " << client_id 
              << " (Total clients: " << clients_.size() << ")" << std::endl;
    
    // 发送欢迎消息
    nlohmann::json welcome_msg = {
        {"type", "welcome"},
        {"client_id", client_id},
        {"message", "Connected to Body Controller WebSocket"},
        {"timestamp", std::time(nullptr)}
    };
    
    SendMessageToClient(client_id, welcome_msg.dump());
}

void WebSocketServer::OnClientDisconnected(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    
    auto it = clients_.find(client_id);
    if (it != clients_.end()) {
        clients_.erase(it);
        std::cout << "[WebSocketServer] Client disconnected: " << client_id 
                  << " (Remaining clients: " << clients_.size() << ")" << std::endl;
    }
}

void WebSocketServer::OnMessageReceived(const std::string& client_id, const std::string& message) {
    std::cout << "[WebSocketServer] Received message from " << client_id << ": " << message << std::endl;
    
    try {
        // 解析JSON消息
        auto json_msg = nlohmann::json::parse(message);
        
        // 处理不同类型的消息
        std::string msg_type = json_msg.value("type", "");
        
        if (msg_type == "ping") {
            // 处理心跳
            nlohmann::json pong_msg = {
                {"type", "pong"},
                {"timestamp", std::time(nullptr)}
            };
            SendMessageToClient(client_id, pong_msg.dump());
            
        } else if (msg_type == "subscribe") {
            // 处理事件订阅
            HandleEventSubscription(client_id, json_msg);
            
        } else if (msg_type == "unsubscribe") {
            // 处理取消订阅
            HandleEventUnsubscription(client_id, json_msg);
            
        } else {
            std::cout << "[WebSocketServer] Unknown message type: " << msg_type << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[WebSocketServer] Error processing message from " << client_id 
                  << ": " << e.what() << std::endl;
    }
}

void WebSocketServer::HandleEventSubscription(const std::string& client_id, const nlohmann::json& message) {
    std::string event_type = message.value("event", "");
    
    if (!event_type.empty()) {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        auto it = clients_.find(client_id);
        if (it != clients_.end()) {
            it->second.subscribed_events.insert(event_type);
            std::cout << "[WebSocketServer] Client " << client_id << " subscribed to: " << event_type << std::endl;
            
            // 发送订阅确认
            nlohmann::json confirm_msg = {
                {"type", "subscription_confirmed"},
                {"event", event_type},
                {"timestamp", std::time(nullptr)}
            };
            SendMessageToClient(client_id, confirm_msg.dump());
        }
    }
}

void WebSocketServer::HandleEventUnsubscription(const std::string& client_id, const nlohmann::json& message) {
    std::string event_type = message.value("event", "");
    
    if (!event_type.empty()) {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        auto it = clients_.find(client_id);
        if (it != clients_.end()) {
            it->second.subscribed_events.erase(event_type);
            std::cout << "[WebSocketServer] Client " << client_id << " unsubscribed from: " << event_type << std::endl;
            
            // 发送取消订阅确认
            nlohmann::json confirm_msg = {
                {"type", "unsubscription_confirmed"},
                {"event", event_type},
                {"timestamp", std::time(nullptr)}
            };
            SendMessageToClient(client_id, confirm_msg.dump());
        }
    }
}

} // namespace web_api
} // namespace body_controller
